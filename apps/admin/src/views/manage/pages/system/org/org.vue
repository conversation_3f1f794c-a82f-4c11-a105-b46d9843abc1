<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { computed, h, onMounted, PropType, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudForm } from '@repo/ui/components/form';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { schoolTypes } from '@repo/infrastructure/data';
  import { useUserStore } from '@repo/infrastructure/store';
  import CryptoJS from 'crypto-js/crypto-js.js';
  import { Message, Modal } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { axiosInstance } from '@repo/infrastructure/request';
  import VueQrcode from '@chenfengyuan/vue-qrcode';
  import { NormalSchoolType } from '@repo/infrastructure/data/schoolTypes';
  // import axios from 'axios';
  // import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';

  const props = defineProps({
    modulePath: {
      type: String,
      required: true,
    },
    visibleColumns: {
      type: Array as PropType<string[]>,
      default: () => [
        'school.name',
        'school.nature',
        'school.type',
        'school.institutionType',
        'school.operationType',
        'school.hasResourceRoom',
      ],
    },
    defaultQueryParams: {
      type: Object,
      default: () => ({ sort: '-id' }),
    },
  });

  const queryParams = computed(() => {
    return {
      ...props.defaultQueryParams,
    };
  });

  const tableRef = ref<any>(null);
  const schema = ref<any>();
  const schoolSchema = ref<any>();
  const editVisible = ref(false);
  const currentEditRow = ref<any>({});
  const crudFormRef = ref<any>(null);
  const userStore = useUserStore();

  const { loading, setLoading } = useLoading();

  const handleShowEdit = (raw?: any) => {
    editVisible.value = true;
    currentEditRow.value = raw.school || {};
  };

  const handleShowResettlementQrCode = async (raw?: any) => {
    Message.loading('生成中，请稍后...');
    try {
      // data is base64 image
      const { data } = await axiosInstance.get(`/resourceCenter/fusionSchool/resettlementQrCode/${raw.school.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.clear();

      Modal.info({
        title: `学生安置二维码 - ${raw.school.name}`,
        content: () => {
          return h('div', { class: 'flex items-center justify-center p-2' }, [
            h('img', { src: `data:image/png;base64,${data}` }),
          ]);
        },
        hideCancel: true,
      });
    } catch (error) {
      Message.clear();
      Message.error('生成失败');
    }
  };

  const handleShowResettlementGeneralQrCode = async (raw?: any) => {
    Message.loading('生成中，请稍后...');
    try {
      // data is base64 image
      const { data } = await axiosInstance.get(`/resourceCenter/fusionSchool/getBoGuid/${raw.school.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.clear();

      const url = `${PROJECT_URLS.MAIN_PROJECT}/module/weappWebView.html?isGuardian=true#/guardian/student/resettlement/apply?boCode=${data}`;

      Modal.info({
        title: `学生安置二维码 - ${raw.school.name}`,
        content: () => {
          // <vue-qrcode :value="url" class="mx-auto" />
          return h('div', { class: 'flex items-center justify-center p-2' }, [h(VueQrcode, { value: url })]);
        },
        hideCancel: true,
      });
    } catch (error) {
      Message.clear();
      Message.error('生成失败');
    }
  };

  const handleRowAction = async (action: any, record?: any) => {
    switch (action.key) {
      case 'add':
      case 'edit':
        handleShowEdit(record || {});
        break;
      case 'resettlementQrCode':
        // handleShowResettlementQrCode(record);
        await handleShowResettlementGeneralQrCode(record);
        break;
      default:
      // pass
    }
  };

  const handleCloseEdit = () => {
    editVisible.value = false;
    currentEditRow.value = {};

    return true;
  };

  const handleSubmit = async (done: any) => {
    setLoading(true);
    try {
      const form = crudFormRef.value.formRef;
      const errors = await form.validate();
      if (errors) {
        return false;
      }
      await crudFormRef.value.handleSubmit(currentEditRow.value);
      done();
    } catch (error) {
      console.error(error);
      return false;
    } finally {
      setLoading(false);
    }

    return true;
  };

  const handleGetRegLink = (branchOffice?: any) => {
    const { userInfo } = userStore;
    let raw = CryptoJS.MD5(new Date().toString()).toString();
    const companyId = userInfo?.company?.id.toString();
    raw = `${raw.substr(0, 16)}-${companyId}-${raw.substr(16, 16)}`;
    let link = `${PROJECT_URLS.MAIN_PROJECT}/?code=${raw}&register=1`;
    let message = `请点击链接注册到 ${userInfo?.company.name}：\n${link}`;
    if (branchOffice) {
      link = `${PROJECT_URLS.MAIN_PROJECT}/?code=${raw}&register=1&bo=${branchOffice.id}`;
      message = `请点击链接注册到 ${branchOffice.name}：\n${link}`;
    }

    const aux = document.createElement('input');
    aux.setAttribute('value', message);
    document.body.appendChild(aux);
    aux.select();
    document.execCommand('copy');
    document.body.removeChild(aux);

    Modal.info({
      title: '提示',
      content: `已复制到剪切板 ${message}`,
      hideCancel: true,
    });
  };

  const handleUploadImport = async (data) => {
    setLoading(true);
    Message.loading('导入中，请稍后...');
    const formData = new FormData();
    formData.set('file', data.fileItem.file);
    formData.set('fileName', data.name);

    try {
      await axiosInstance.post('/org/branchOffice/import', formData, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      Message.clear();
      Message.success('导入成功');
    } finally {
      setLoading(false);
    }
  };

  const handleImportDisStudentNum = async (data) => {
    setLoading(true);
    Message.loading('导入中，请稍后...');
    const formData = new FormData();
    formData.set('file', data.fileItem.file);
    formData.set('fileName', data.name);
    try {
      await axiosInstance.post('/org/branchOffice/importDisStudentNum', formData, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      Message.clear();
      Message.success('导入成功');
    } finally {
      setLoading(false);
    }
  };

  const getDisplayType = (raw: any): string => {
    return ['初中', '高中'].includes(raw.type) ? '中学' : raw.type;
  };

  const setTypeFromSelect = (val: string, raw: any) => {
    if (val === '中学') {
      raw.type = '初中';
    } else {
      raw.type = val;
    }
  };

  const modelUrl = 'https://tejiao-prod-static1.oss-cn-chengdu.aliyuncs.com/templates/学校导入模板.xlsx';
  const modelDisNumImportUrl =
    'https://tejiao-prod-static1.oss-cn-chengdu.aliyuncs.com/templates/残疾学生数导入模版.xlsx';

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/org/branchOffice');
    schoolSchema.value = await SchemaHelper.getInstanceByDs('/resourceCenter/fusionSchool');
  });
</script>

<template>
  <a-card v-if="schema && schoolSchema">
    <template #title>
      <div class="flex justify-between gap-2">
        <div>单位列表</div>
        <table-action
          v-if="tableRef"
          class="flex-1"
          :module-path="modulePath"
          :schema="schema"
          :table="tableRef"
          component-size="mini"
          @row-action="handleRowAction"
        >
          <template #extra-actions>
            <a-button size="mini" @click="() => handleGetRegLink()">复制注册链接</a-button>
            <a-dropdown trigger="hover">
              <a-button size="mini"
                ><template #icon><icon-download /></template>模版下载</a-button
              >
              <template #content>
                <a-doption>
                  <a-button size="mini">
                    <a :href="schema?.importable?.template || modelUrl" download><icon-file />单位模版</a>
                  </a-button>
                </a-doption>
                <a-doption>
                  <a-button size="mini">
                    <a :href="modelUrl || modelDisNumImportUrl" download><icon-file />残疾学生数量模版</a>
                  </a-button>
                </a-doption>
              </template>
            </a-dropdown>

            <a-upload
              v-if="false && userStore.isAuthorized('system:org:org:import')"
              :show-file-list="false"
              accept=".xlsx,.xls"
              :custom-request="handleUploadImport"
            >
              <template #upload-button>
                <a-button size="mini"
                  ><template #icon><IconImport /></template>导入</a-button
                >
              </template>
            </a-upload>

            <a-dropdown trigger="hover">
              <a-button size="mini"
                ><template #icon><IconImport /></template>导入</a-button
              >
              <template #content>
                <a-doption>
                  <a-upload
                    v-if="userStore.isAuthorized('system:org:org:import')"
                    :show-file-list="false"
                    accept=".xlsx,.xls"
                    :custom-request="handleUploadImport"
                  >
                    <template #upload-button>
                      <a-button size="mini"
                        ><template #icon><icon-attachment /></template>单位导入</a-button
                      >
                    </template>
                  </a-upload>
                </a-doption>
                <a-doption>
                  <a-upload
                    v-if="userStore.isAuthorized('system:org:org:import')"
                    :show-file-list="false"
                    accept=".xlsx,.xls"
                    :custom-request="handleImportDisStudentNum"
                  >
                    <template #upload-button>
                      <a-button size="mini"
                        ><template #icon><icon-attachment /></template>残疾学生数量导入</a-button
                      >
                    </template>
                  </a-upload>
                </a-doption>
              </template>
            </a-dropdown>
          </template>
        </table-action>
      </div>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      size="mini"
      :module-path="modulePath"
      :row-selection="false"
      :default-query-params="queryParams"
      :visible-columns="visibleColumns"
      @row-action="handleRowAction"
    />
    <a-modal
      id="org-drop-down-parent-id"
      v-model:visible="editVisible"
      :title="currentEditRow.id ? '编辑单位' : '新增单位'"
      :ok-loading="loading"
      :on-before-ok="handleSubmit"
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-cancel="handleCloseEdit"
      :on-before-close="handleCloseEdit"
      width="60%"
    >
      <crud-form
        v-if="editVisible"
        ref="crudFormRef"
        v-model="currentEditRow"
        :schema="schoolSchema"
        :show-actions="false"
        :callback="handleCloseEdit"
      >
        <!--        <template #prepend="{ formData }">-->
        <!--          <a-card :bordered="false">-->
        <!--            <a-form-item v-if="!formData.id" label="上级单位">-->
        <!--              <tree-select-input v-model="formData.parentId" :schema-field="schoolSchema.parentId" />-->
        <!--            </a-form-item>-->
        <!--          </a-card>-->
        <!--        </template>-->
        <template #custom-input-type="{ raw }">
          <div>
            <!-- Select 显示内容取决于 raw.type -->
            <a-select
              v-if="['普通教育学校', '特殊教育学校'].includes(raw.nature)"
              :model-value="getDisplayType(raw)"
              :options="schoolTypes.commonSchoolTypes"
              @update:model-value="(val) => setTypeFromSelect(val, raw)"
            />

            <!-- 职业学校直接绑定 -->
            <a-select
              v-else-if="raw.nature === '职业教育学校'"
              v-model="raw.type"
              :options="schoolTypes.vocationalSchoolTypes"
            />

            <div v-else>仅普校、特校及职校需选择</div>

            <div>
              <a-radio-group v-if="getDisplayType(raw) === '中学'" v-model="raw.type">
                <a-radio v-for="item in ['初中', '高中']" :key="item" :value="item">
                  {{ item }}
                </a-radio>
              </a-radio-group>
            </div>
          </div>
        </template>
        <!--        <template #custom-input-type="{ raw }">-->
        <!--          <div>-->
        <!--            <a-select-->
        <!--              v-if="['普通教育学校', '特殊教育学校'].includes(raw.nature)"-->
        <!--              v-model="type"-->
        <!--              :options="schoolTypes.commonSchoolTypes"-->
        <!--              @change="(val) => (selectedType = val)"-->
        <!--            />-->
        <!--            <a-select-->
        <!--              v-else-if="raw.nature === '职业教育学校'"-->
        <!--              v-model="raw.type"-->
        <!--              :options="schoolTypes.vocationalSchoolTypes"-->
        <!--            />-->
        <!--            <div v-else>仅普校、特校及职校需选择</div>-->
        <!--            <div>-->
        <!--              <a-radio-group>-->
        <!--                <a-radio v-for="item in ['初中', '高中']" :key="item" v-model="selectedType" :value="item">-->
        <!--                  {{ item }}{{ selectedType }}-->
        <!--                </a-radio>-->
        <!--              </a-radio-group>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </template>-->
        <template #custom-input-institutionType="{ raw }">
          <a-select v-if="raw.nature === '机构'" v-model="raw.type" :options="schoolTypes.institutionTypes" />
          <div v-else>仅机构需选择</div>
        </template>
      </crud-form>
    </a-modal>
  </a-card>
</template>

<style scoped lang="less"></style>
